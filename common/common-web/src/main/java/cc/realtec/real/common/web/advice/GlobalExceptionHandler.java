package cc.realtec.real.common.web.advice;

import cc.realtec.real.common.web.constants.ResultCode;
import cc.realtec.real.common.web.exception.ApiException;
import cc.realtec.real.common.web.model.ApiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.NoHandlerFoundException;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ApiResult<?>> notHandleExceptionHandler(NoHandlerFoundException noHandlerFoundException) {
        log.error("not handle exception", noHandlerFoundException);
        ApiResult<?> response = ApiResult.of(ResultCode.NOT_FOUND.getCode(), noHandlerFoundException.getMessage());
        return ResponseEntity.status(ResultCode.NOT_FOUND.getStatus()).body(response);
    }

//    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResult<?>> exceptionHandler(Exception e) {
        log.error("Fail: ", e);
        ApiResult<?> response = ApiResult.of(ResultCode.FAIL.getCode(), e.getMessage());
        return ResponseEntity.status(ResultCode.FAIL.getStatus()).body(response);
    }

    @ExceptionHandler(ApiException.class)
    public ResponseEntity<ApiResult<?>> apiExceptionHandler(ApiException e) {
        log.error(e.getCode(), e.getMessage());
        ApiResult<?> response = ApiResult.of(e.getCode(), e.getMessage());
        return ResponseEntity.status(e.getStatus()).body(response);
    }
}
