package cc.realtec.real.bff.mvc.config;

import cc.realtec.real.auth.client.security.JdbcClientRegistrationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.security.oauth2.client.OAuth2ClientProperties;
import org.springframework.boot.autoconfigure.security.oauth2.client.OAuth2ClientPropertiesMapper;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.client.oidc.web.logout.OidcClientInitiatedLogoutSuccessHandler;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.DelegatingAuthenticationEntryPoint;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.security.web.authentication.logout.*;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
import org.springframework.security.web.csrf.CsrfLogoutHandler;
import org.springframework.security.web.csrf.CsrfTokenRepository;
import org.springframework.security.web.csrf.CsrfTokenRequestAttributeHandler;
import org.springframework.security.web.util.matcher.MediaTypeRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;

@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(OAuth2ClientProperties.class)
public class SecurityConfig {
    private final OAuth2ClientProperties oauth2ClientProperties;
    CookieCsrfTokenRepository cookieCsrfTokenRepository = CookieCsrfTokenRepository.withHttpOnlyFalse();
    CsrfTokenRequestAttributeHandler csrfTokenRequestAttributeHandler = new CsrfTokenRequestAttributeHandler();

    @Value("${app.base-url}")
    private String appBaseUrl;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http,
                                                   JdbcClientRegistrationRepository jdbcClientRegistrationRepository
    ) throws Exception {
        ClientRegistration clientRegistration = jdbcClientRegistrationRepository.findByRegistrationId("main-client-code");
        if (clientRegistration == null) {
            List<ClientRegistration> registrations = new ArrayList<>(
                    new OAuth2ClientPropertiesMapper(oauth2ClientProperties).asClientRegistrations().values());
            Optional<ClientRegistration> first = registrations.stream().filter(registration -> registration.getRegistrationId().equals("main-client-code")).findFirst();
            if (first.isPresent()) {
                jdbcClientRegistrationRepository.save(first.get());
            } else {
                throw new RuntimeException("main-client-code not found");
            }
        }

        http.authorizeHttpRequests(authorize -> authorize
                        .requestMatchers("/actuator/**").permitAll()
                        .requestMatchers("/favicon.ico").permitAll()
                        .anyRequest().authenticated())
                .cors(Customizer.withDefaults())
                .csrf(csrf->csrf.csrfTokenRepository(cookieCsrfTokenRepository)
                        .csrfTokenRequestHandler(csrfTokenRequestAttributeHandler))
                .exceptionHandling(exception -> exception
                        .authenticationEntryPoint(authenticationEntryPoint()))
                .oauth2Login(oauth2Login -> oauth2Login
                        .successHandler(new SimpleUrlAuthenticationSuccessHandler(this.appBaseUrl)))
                .logout(logout -> logout
                                .clearAuthentication(true)
                                .deleteCookies("JSESSIONID")
//                        .deleteCookies("JSESSIONID", "XSRF-TOKEN")
                                .addLogoutHandler(logoutHandler(cookieCsrfTokenRepository))
                                .logoutSuccessHandler(oidcLogoutSuccessHandler(jdbcClientRegistrationRepository))
                )
                .oauth2Client(Customizer.withDefaults());
        return http.build();
    }

    private LogoutSuccessHandler oidcLogoutSuccessHandler(JdbcClientRegistrationRepository jdbcClientRegistrationRepository) {
        OidcClientInitiatedLogoutSuccessHandler logoutSuccessHandler = new OidcClientInitiatedLogoutSuccessHandler(jdbcClientRegistrationRepository);
        logoutSuccessHandler.setPostLogoutRedirectUri(appBaseUrl);
        return logoutSuccessHandler;
    }

    private AuthenticationEntryPoint authenticationEntryPoint() {
        AuthenticationEntryPoint authenticationEntryPoint =
                new LoginUrlAuthenticationEntryPoint("/oauth2/authorization/main-client-code");
        MediaTypeRequestMatcher textHtmlMatcher = new MediaTypeRequestMatcher(MediaType.TEXT_HTML);
        textHtmlMatcher.setUseEquals(true);

        LinkedHashMap<RequestMatcher, AuthenticationEntryPoint> entryPoints = new LinkedHashMap<>();
        entryPoints.put(textHtmlMatcher, authenticationEntryPoint);

        DelegatingAuthenticationEntryPoint delegatingAuthenticationEntryPoint = new DelegatingAuthenticationEntryPoint(entryPoints);
        delegatingAuthenticationEntryPoint.setDefaultEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED));
        return delegatingAuthenticationEntryPoint;
    }

//    private LogoutHandler revoketokenLogoutHandler(JdbcClientRegistrationRepository jdbcClientRegistrationRepository) {
//       return (request, response, authentication) -> {
//            if (authentication != null) {
//                String clientRegistrationId = ((OAuth2AuthenticationToken) authentication).getAuthorizedClientRegistrationId();
//                String principalName = authentication.getName();
//                try {
//                    TokenRevocationService tokenRevocationService = new TokenRevocationService(jdbcClientRegistrationRepository, authorizedClientService);
//                    tokenRevocationService.revokeToken(clientRegistrationId, principalName);
//                } catch (Exception e) {
//                    log.error("
//    }

    private LogoutHandler logoutHandler(CsrfTokenRepository csrfTokenRepository) {
        return new CompositeLogoutHandler(
                new SecurityContextLogoutHandler(),
                new CsrfLogoutHandler(csrfTokenRepository)
        );
    }
}