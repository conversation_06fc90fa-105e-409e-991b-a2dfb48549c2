package cc.realtec.real.bff.mvc.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.OAuth2RefreshToken;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpEntity;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
public class LogoutController {

    private final OAuth2AuthorizedClientService authorizedClientService;
    
    @Value("${app.base-url}")
    private String appBaseUrl;

    @PostMapping("/api/logout")
    public ResponseEntity<Map<String, String>> logout(HttpServletRequest request, 
                                                     HttpServletResponse response, 
                                                     Authentication authentication) {
        log.info("Processing logout request for user: {}", 
                authentication != null ? authentication.getName() : "anonymous");

        try {
            // Revoke tokens if OAuth2 authentication
            if (authentication instanceof OAuth2AuthenticationToken oauth2Token) {
                revokeTokens(oauth2Token);
            }

            // Clear security context
            SecurityContextLogoutHandler logoutHandler = new SecurityContextLogoutHandler();
            logoutHandler.logout(request, response, authentication);

            // Clear additional cookies
            clearCookies(response);

            log.info("Logout completed successfully");
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Logged out successfully",
                "redirectUrl", appBaseUrl
            ));

        } catch (Exception e) {
            log.error("Error during logout: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(
                        "status", "error",
                        "message", "Logout failed: " + e.getMessage()
                    ));
        }
    }

    private void revokeTokens(OAuth2AuthenticationToken oauth2Token) {
        try {
            String clientRegistrationId = oauth2Token.getAuthorizedClientRegistrationId();
            String principalName = oauth2Token.getName();

            OAuth2AuthorizedClient authorizedClient = authorizedClientService
                    .loadAuthorizedClient(clientRegistrationId, principalName);

            if (authorizedClient != null) {
                OAuth2AccessToken accessToken = authorizedClient.getAccessToken();
                OAuth2RefreshToken refreshToken = authorizedClient.getRefreshToken();

                // Remove the authorized client
                authorizedClientService.removeAuthorizedClient(clientRegistrationId, principalName);

                log.info("Tokens revoked for client: {} and user: {}", clientRegistrationId, principalName);
            }
        } catch (Exception e) {
            log.warn("Failed to revoke tokens: {}", e.getMessage());
        }
    }

    private void clearCookies(HttpServletResponse response) {
        // Clear session cookie
        response.addHeader("Set-Cookie", "JSESSIONID=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax");
        
        // Clear CSRF token cookie
        response.addHeader("Set-Cookie", "XSRF-TOKEN=; Path=/; Max-Age=0; SameSite=Lax");
        
        // Clear any other potential OAuth2 related cookies
        response.addHeader("Set-Cookie", "OAuth2AuthorizedClient=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax");
    }
}
